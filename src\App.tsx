import { useState, useRef, useEffect, ChangeEvent } from 'react';
import { <PERSON>R<PERSON>, Loader2, Image as ImageIcon, PanelRightOpen } from 'lucide-react';
import { PumpFunWidget } from './components/PumpFunWidget';
import { DexScreenerWidget } from './components/DexScreenerWidget';
import { PhantomWidget } from './components/PhantomWidget';
import { JupiterWidget } from './components/JupiterWidget';
import { TokenChartWidget } from './components/TokenChartWidget';
import { ExternalWalletWidget } from './components/ExternalWalletWidget';
import { ThinkingDisplay } from './components/ThinkingDisplay';
import { RightSidebar } from './components/RightSidebar';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import clsx from 'clsx';
import type { Message } from './types';
import { useMessages } from './contexts/MessagesContext';
import { getChatCompletion } from './lib/ai';

function readFileAsDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

function App() {
  const [input, setInput] = useState('');
  const { messages, setMessages, activeWidgetId, setActiveWidgetId, currentThreadId, setCurrentThreadId } = useMessages();
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [showRightSidebar, setShowRightSidebar] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // ✅ INTELLIGENT MEMORY: Backend now uses Mem0 for contextual awareness
  // No more manual context hints needed - Mem0 handles everything automatically!

  // Widget creation function for sidebar
  const createWidget = (widgetType: string) => {
    console.log(`📱 createWidget called with: ${widgetType}`);
    console.log(`📱 Current thread ID before: ${currentThreadId}`);

    // Get or create thread ID
    let threadId = currentThreadId;
    if (!threadId) {
      threadId = `thread_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      console.log(`📱 Created new thread ID: ${threadId}`);
      setCurrentThreadId(threadId);
    }

    console.log(`📱 Using thread ID: ${threadId}`);

    // Generate unique ID with random component and widget type to avoid collisions
    const uniqueId = `widget_${widgetType}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const widgetMessage: Message = {
      id: uniqueId,
      content: widgetType,
      type: 'widget',
      timestamp: new Date(),
      threadId: threadId,
      widgetData: {}
    };

    console.log(`📱 Created widget message:`, widgetMessage);
    console.log(`📱 Widget message threadId: ${widgetMessage.threadId}`);

    setMessages(prev => {
      const newMessages = [...prev, widgetMessage];
      console.log(`📱 Updated messages array, new length: ${newMessages.length}`);
      console.log(`📱 Last message:`, newMessages[newMessages.length - 1]);

      // Check if the new message will be visible with the thread ID we're using
      const visibleMessages = newMessages.filter(msg => msg.threadId === threadId);
      console.log(`📱 Messages visible with threadId ${threadId}: ${visibleMessages.length}`);

      return newMessages;
    });

    setActiveWidgetId(widgetMessage.id);
    console.log(`📱 Set active widget ID: ${widgetMessage.id}`);
  };

  // Get or create thread ID for new messages
  const getThreadId = () => {
    console.log('🔍 getThreadId called - currentThreadId:', currentThreadId);
    if (currentThreadId) {
      console.log('🔄 Returning existing thread ID:', currentThreadId);
      return currentThreadId;
    }
    const newThreadId = `thread_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log('🆕 Creating new thread ID:', newThreadId);
    console.log('🆕 Setting currentThreadId to:', newThreadId);
    setCurrentThreadId(newThreadId);
    return newThreadId;
  };

  const handleDexScreenerClick = () => {
    const widgetMessage: Message = {
      id: `widget_dexscreener_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: 'dexscreener',
      type: 'widget' as const,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, widgetMessage]);
    setActiveWidgetId(widgetMessage.id);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    if (!isInitialLoad) {
      scrollToBottom();
    }
  }, [messages, isLoading]);

  useEffect(() => {
    if (messages.length > 0) {
      setIsInitialLoad(false);
    }
  }, [messages]);
  
  const handlePumpFunClick = () => {
    const widgetMessage: Message = {
      id: `widget_pumpfun_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: 'pumpfun',
      type: 'widget' as const,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, widgetMessage]);
    setActiveWidgetId(widgetMessage.id);
  };

  const handleJupiterClick = () => {
    const widgetMessage: Message = {
      id: `widget_jupiter_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: 'jupiter',
      type: 'widget' as const,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, widgetMessage]);
    setActiveWidgetId(widgetMessage.id);
  };

  const handleImageUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        const dataUrl = await readFileAsDataURL(file);
        setSelectedImage(dataUrl);
      } catch (error) {
        console.error('Error reading file:', error);
      }
    }
  };

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const removeImage = () => {
    setSelectedImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async () => {
    if (!input.trim() || isLoading) return;

    // Auto-create thread if none exists (like modern chat apps)
    let activeThreadId = currentThreadId;
    if (!activeThreadId) {
      activeThreadId = getThreadId();
    }

    // Handle /pumpfun command
    if (input.trim().toLowerCase() === '/pumpfun') {
      const widgetMessage: Message = {
        id: `widget_pumpfun_cmd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        content: 'pumpfun',
        type: 'widget',
        timestamp: new Date(),
        threadId: activeThreadId // Assign widget to current thread
      };
      setMessages(prev => [...prev, widgetMessage]);
      setActiveWidgetId(widgetMessage.id);
      setInput('');
      return;
    }
    // Keep user messages completely clean - backend should handle context intelligently
    let messageContent = input.trim();

    const userMessage: Message = {
      id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      content: messageContent,
      type: 'user',
      timestamp: new Date(),
      image: selectedImage,
      threadId: activeThreadId
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setSelectedImage(null);
    setIsLoading(true);

    try {
      // Non-streaming: call Deep Agent / OpenRouter fallback
      const aiMessages = messages
        .concat(userMessage)
        .filter(msg => msg.type !== 'widget');

      const response = await getChatCompletion(aiMessages, activeThreadId);

      // Assistant message (content)
      if (response.content) {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: response.content,
          type: 'assistant',
          timestamp: new Date(),
          threadId: activeThreadId,
          thinking: response.thinking,
          model_info: response.model_info
        };
        setMessages(prev => [...prev, assistantMessage]);
      }

      // Handle function call → widget
      if (response.function_call) {
        let widgetContent = 'dexscreener';
        let widgetData: any = undefined;
        if (response.function_call.name === 'showPumpFunWidget') {
          widgetContent = 'pumpfun';
        } else if (response.function_call.name === 'showDexScreenerWidget') {
          widgetContent = 'dexscreener';
        } else if (response.function_call.name === 'showJupiterWidget') {
          widgetContent = 'jupiter';
        } else if (response.function_call.name === 'showPhantomWidget') {
          widgetContent = 'phantom';
        } else if (response.function_call.name === 'showExternalWalletWidget') {
          widgetContent = 'external_wallet';
        } else if (response.function_call.name === 'showTokenChartWidget') {
          widgetContent = 'tokenchart';
          widgetData = {
            tokenAddress: response.function_call.arguments?.tokenAddress || '',
            chain: response.function_call.arguments?.chain || 'sol'
          };
        }

        const widgetMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: widgetContent,
          type: 'widget',
          timestamp: new Date(),
          threadId: activeThreadId,
          widgetData
        };
        setMessages(prev => [...prev, widgetMessage]);
        setActiveWidgetId(widgetMessage.id);
      }

    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "I apologize, but I encountered an error while processing your request. Please try again.",
        type: 'assistant',
        timestamp: new Date(),
        threadId: activeThreadId
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div className="flex-1 flex h-screen overflow-hidden">

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 overflow-y-auto">
          <div className="px-8 pt-8">
              {(() => {
                const currentThreadMessages = currentThreadId
                  ? messages.filter(msg => msg.threadId === currentThreadId)
                  : [];

                return currentThreadMessages.length === 0 ? (
                  <div className="flex-1 flex flex-col items-center justify-center text-center">
                    <h1 className="text-[64px] font-semibold mb-4 tracking-[-0.02em] bg-gradient-to-r from-white to-gray-500 text-transparent bg-clip-text">
                      {currentThreadId ? "How can I assist you?" : "Welcome to Chad GPT"}
                    </h1>
                    {!currentThreadId ? (
                      <p className="text-[#888] text-lg mb-8">
                        Start a new conversation or select from your chat history.
                      </p>
                    ) : (
                      <p className="text-[#888] text-lg mb-8">
                        Ask me anything! I can show widgets, analyze data, help with code, and more.
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="space-y-6">
                    {currentThreadMessages.map((message) => (
                  <div
                    key={message.id}
                    className={clsx('animate-fade-in', {
                      'p-4': message.type === 'widget'
                    })}
                  >
                    {message.type === 'widget' ? (
                      message.content === 'pumpfun' ? (
                        <div className={clsx(message.id !== activeWidgetId && 'opacity-50')}>
                          <PumpFunWidget isActive={message.id === activeWidgetId} />
                        </div>
                      ) : message.content === 'dexscreener' ? (
                        <div className={clsx(message.id !== activeWidgetId && 'opacity-50')}>
                          <DexScreenerWidget isActive={message.id === activeWidgetId} />
                        </div>
                      ) : message.content === 'jupiter' ? (
                        <div className={clsx(message.id !== activeWidgetId && 'opacity-50')}>
                          <JupiterWidget />
                        </div>
                      ) : message.content === 'phantom' ? (
                        <div className={clsx(message.id !== activeWidgetId && 'opacity-50')}>
                          <PhantomWidget />
                        </div>
                      ) : message.content === 'external_wallet' ? (
                        <div className={clsx(message.id !== activeWidgetId && 'opacity-50')}>
                          <ExternalWalletWidget isActive={message.id === activeWidgetId} />
                        </div>
                      ) : message.content === 'tokenchart' ? (
                        <div className={clsx(message.id !== activeWidgetId && 'opacity-50')}>
                          <TokenChartWidget
                            tokenAddress={message.widgetData?.tokenAddress || ''}
                            chain={message.widgetData?.chain}
                            isActive={message.id === activeWidgetId}
                          />
                        </div>
                      ) : null
                    ) : (
                      <div className="w-full max-w-[95%] sm:max-w-[90%] mx-auto px-4">
                        <div className={clsx('flex gap-4 p-4', {
                          'flex-row-reverse ml-12': message.type === 'user',
                          'bg-[#111] rounded-2xl shadow-lg shadow-black/10': message.type === 'assistant'
                        })}>
                          <div className={clsx(
                            'w-9 h-9 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm shadow-black/20',
                            message.type === 'assistant' ? 'bg-gradient-to-br from-[#222] to-[#333]' : 'bg-gradient-to-br from-indigo-500 to-purple-500'
                          )}>
                            {message.type === 'assistant' ? (
                              <img
                                src="https://pump.mypinata.cloud/ipfs/QmY2HUM8HMPytN2cJDr9XxpGDzoCmp9WNszFpeB8N15NR9?img-width=800&img-dpr=2&img-onerror=redirect"
                                alt="Chad GPT"
                                className="w-full h-full rounded-xl object-cover"
                              />
                            ) : (
                              <svg
                                viewBox="0 0 24 24"
                                fill="none"
                                className="w-5 h-5 text-white"
                                stroke="currentColor"
                                strokeWidth="2"
                              >
                                <path
                                  d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <circle cx="12" cy="7" r="4" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                            )}
                          </div>
                          <div className={clsx('flex-1 space-y-2')}>
                            <div className={clsx('flex items-center gap-2', {
                              'justify-end': message.type === 'user'
                            })}>
                              <span className="text-sm font-medium">
                                {message.type === 'user' ? 'User' : '@chadgpt'}
                              </span>
                              <span className="text-xs text-[#666]">
                                {new Date(message.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                            <div className={clsx({
                              'flex justify-end': message.type === 'user'
                            })}>
                              {message.type === 'user' ? (
                                <p className="bg-[#22c55e] text-black px-4 py-2 rounded-2xl max-w-[95%] sm:max-w-[90%] text-[13px] leading-relaxed break-words text-left">
                                  {message.content}
                                </p>
                              ) : (
                                <div className="text-[13px] leading-relaxed break-words max-w-[95%] sm:max-w-[90%]">
                                  {/* Show thinking process if available */}
                                  {message.thinking && (
                                    <ThinkingDisplay
                                      thinking={message.thinking}
                                      modelInfo={message.model_info}
                                    />
                                  )}
                                  <ReactMarkdown
                                    remarkPlugins={[remarkGfm]}
                                    components={{
                                      // Custom styling for markdown elements
                                      h1: ({children}) => <h1 className="text-lg font-bold mb-3 text-white break-words">{children}</h1>,
                                      h2: ({children}) => <h2 className="text-base font-bold mb-2 text-white break-words">{children}</h2>,
                                      h3: ({children}) => <h3 className="text-sm font-bold mb-2 text-white break-words">{children}</h3>,
                                      p: ({children}) => <p className="mb-2 text-white break-words leading-relaxed">{children}</p>,
                                      ul: ({children}) => <ul className="list-disc list-inside mb-2 text-white space-y-1">{children}</ul>,
                                      ol: ({children}) => <ol className="list-decimal list-inside mb-2 text-white space-y-1">{children}</ol>,
                                      li: ({children}) => <li className="text-white break-words leading-relaxed">{children}</li>,
                                      strong: ({children}) => <strong className="font-bold text-white">{children}</strong>,
                                      em: ({children}) => <em className="italic text-white">{children}</em>,
                                      code: ({children}) => <code className="bg-gray-800 text-green-400 px-1 py-0.5 rounded text-xs font-mono">{children}</code>,
                                      pre: ({children}) => <pre className="bg-gray-800 p-3 rounded-lg overflow-x-auto mb-2 text-sm">{children}</pre>,
                                      blockquote: ({children}) => <blockquote className="border-l-4 border-gray-600 pl-4 italic text-gray-300 mb-2 break-words">{children}</blockquote>,
                                      a: ({children, href}) => <a href={href} className="text-blue-400 hover:text-blue-300 underline break-all" target="_blank" rel="noopener noreferrer">{children}</a>
                                    }}
                                  >
                                    {message.content}
                                  </ReactMarkdown>
                                </div>
                              )}
                            </div>
                            {message.image && (
                              <div className={clsx('mt-3', {
                                'flex justify-end': message.type === 'user'
                              })}>
                                <img
                                  src={message.image}
                                  alt="Uploaded"
                                  className="max-w-[200px] rounded-lg"
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  ))}
                  {isLoading && !currentThreadMessages.some(msg => msg.type === 'widget') && !currentThreadMessages.find(msg => msg.type === 'assistant' && msg.id === (Date.now() + 1).toString()) && (
                    <div className="flex items-center gap-2 text-[#666] animate-pulse p-4">
                      <Loader2 size={16} className="animate-spin" />
                      <span className="text-sm">Loading...</span>
                    </div>
                  )}
                </div>
              );
            })()}
            <div ref={messagesEndRef} />
          </div>
          {messages.length === 0 && (
            <>
              <div className="px-8 pt-8 bg-black">
                <div className="w-full max-w-[95%] sm:max-w-[90%] mx-auto">
                  <h2 className="text-xs text-[#666] mb-4 font-medium">Suggestions</h2>
                  <div className="grid grid-cols-2 gap-4">
                    <SuggestionCard
                      title="Any updates from @phantom recently?"
                      description="summarize the latest tweets from @phantom"
                    />
                    <SuggestionCard
                      title="Launch a new token"
                      description="deploy a new token on pump.fun"
                      command="/pumpfun"
                    />
                    <SuggestionCard
                      title="What has toly been doing recently?"
                      description="summarize his recent tweets"
                    />
                    <SuggestionCard
                      title="Swap 1 SOL for USDC"
                      description="using Jupiter to swap on Solana"
                    />
                  </div>
                </div>
              </div>

              {/* Integrations */}
              <div className="px-8 py-8 bg-black">
                <div className="w-full max-w-[95%] sm:max-w-[90%] mx-auto">
                  <h2 className="text-xs text-[#666] mb-4 font-medium">Integrations</h2>
                  <div className="grid grid-cols-2 gap-4">
                  <IntegrationCard
                    icon={<img src="https://upload.wikimedia.org/wikipedia/en/b/bd/Pump_fun_logo.png" alt="pump.fun" className="w-8 h-8 rounded-lg" />}
                    title="pump.fun"
                    description="Discover new tokens, launch tokens"
                    onClick={handlePumpFunClick}
                  />
                  <IntegrationCard
                    icon={<img src="https://cdn.prod.website-files.com/61a98989a418f6f2acefef70/63fa6a1c8bac0aa6f94e8d39_637addd51e7af00944eaf7c5_Zac3eqf6UNKAugFqEFNhq6FvCe6PYruuBkNed1UXFVk.jpeg" alt="Magic Eden" className="w-8 h-8 rounded-lg object-cover" />}
                    title="Magic Eden"
                    description="Explore the best NFT collections"
                  />
                  <IntegrationCard
                    icon={<img src="https://dexscreener.com/favicon.ico" alt="DexScreener" className="w-8 h-8 rounded-lg" />}
                    title="DexScreener"
                    description="Real-time DEX trading charts & data"
                    onClick={handleDexScreenerClick}
                  />
                  <IntegrationCard
                    icon={<img
                      src="https://i.postimg.cc/mgVdkZsV/phantom.png"
                      alt="Phantom"
                      className="w-8 h-8 rounded-lg object-contain bg-[#222]"
                    />}
                    title="Phantom"
                    description="Connect wallet, sign messages & transactions"
                    onClick={() => {
                      setMessages(prev => prev.filter(msg => msg.type !== 'widget'));
                      const widgetMessage: Message = {
                        id: `widget_phantom_starter_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                        content: 'phantom',
                        type: 'widget',
                        timestamp: new Date()
                      };
                      setMessages(prev => [...prev, widgetMessage]);
                      setActiveWidgetId(widgetMessage.id);
                    }}
                  />
                
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
        {/* Input Area */}
        <div className="p-8 bg-black sticky bottom-0">
          <div className="w-full max-w-[95%] sm:max-w-[90%] mx-auto">
            <input
              type="file"
              accept="image/*"
              className="hidden"
              ref={fileInputRef}
              onChange={handleImageUpload}
            />
            {selectedImage && (
              <div className="mb-4 relative">
                <img
                  src={selectedImage}
                  alt="Selected"
                  className="max-w-[200px] rounded-lg"
                />
                <button
                  onClick={removeImage}
                  className="absolute top-2 right-2 bg-black/50 rounded-full p-1 hover:bg-black/75 transition-colors"
                >
                  ×
                </button>
              </div>
            )}
            <div className="relative">
              <textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full bg-[#111] rounded-2xl p-6 text-white resize-none h-24 focus:outline-none focus:ring-1 focus:ring-[#222] text-sm leading-relaxed transition-shadow duration-200 shadow-lg shadow-black/10"
                placeholder="Message Chad GPT..."
              />
              <div className="absolute bottom-6 right-6 flex items-center gap-3 text-[#666]">
            <button
              onClick={handleImageClick}
              className="p-2 rounded-lg transition-all duration-200 hover:bg-[#222]"
            >
              <ImageIcon size={16} />
            </button>
            <span className="text-sm">{input.length}/2000</span>
            <button
              onClick={handleSubmit}
              disabled={isLoading || (!input.trim() && !selectedImage)}
              className={clsx(
                "p-2 rounded-lg transition-all duration-200",
                (input.length > 0 || selectedImage) && !isLoading ? "bg-white text-black hover:bg-gray-100" : "bg-[#222]",
                "disabled:opacity-50 disabled:cursor-not-allowed"
              )}
            >
              {isLoading ? (
                <Loader2 size={16} className="animate-spin" />
              ) : (
                <ArrowRight size={16} />
              )}
            </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Sidebar */}
      {showRightSidebar && (
        <RightSidebar
          onClose={() => setShowRightSidebar(false)}
          onCreateWidget={createWidget}
        />
      )}

      {/* Floating sidebar toggle button */}
      {!showRightSidebar && (
        <button
          onClick={() => setShowRightSidebar(true)}
          className="fixed top-4 right-4 p-3 bg-[#111] hover:bg-[#181818] rounded-lg transition-all duration-200 text-[#888] hover:text-white shadow-lg shadow-black/20 hover:scale-105 active:scale-95 z-50 hidden lg:flex"
          aria-label="Show sidebar"
        >
          <PanelRightOpen size={20} />
        </button>
      )}
    </div>
  );
}

function SuggestionCard({ title, description, command }: { title: string; description: string; command?: string }) {
  return (
    <div className="bg-[#111] rounded-2xl p-5 hover:bg-[#181818] cursor-pointer transition-all duration-200 shadow-sm shadow-black/20 hover:shadow-md hover:shadow-black/30">
      <h3 className="text-sm font-semibold mb-2">{title}</h3>
      <p className="text-sm text-[#666] mb-2">{description}</p>
      {command && (
        <div className="text-xs text-[#444] font-mono">{command}</div>
      )}
    </div>
  );
}

function IntegrationCard({ 
  icon, 
  title, 
  description, 
  onClick 
}: { 
  icon: React.ReactNode; 
  title: string; 
  description: string;
  onClick?: () => void;
}) {
  return (
    <div 
      onClick={onClick}
      className="bg-[#111] rounded-2xl p-5 hover:bg-[#181818] cursor-pointer transition-all duration-200 shadow-sm shadow-black/20 hover:shadow-md hover:shadow-black/30"
    >
      <div className="flex items-center gap-3">
        {icon}
        <div>
          <h3 className="text-sm font-semibold">{title}</h3>
          <p className="text-[11px] text-[#666]">{description}</p>
        </div>
      </div>
    </div>
  );
}

export default App