import { useState, useEffect } from 'react';
import { Search, ArrowLeft, ExternalLink, Globe, Twitter, MessageCircle, Info, TrendingUp } from 'lucide-react';
import clsx from 'clsx';
import { useMessages } from '../contexts/MessagesContext';

import { registerWidgetContext, updateWidgetContext, formatDexScreenerContext } from '../lib/widgetContext';

// Category types for DexScreener
type DexScreenerCategory = 'new' | 'boosted';

// Sort options for DexScreener
type SortOption = 'marketCap' | 'volume' | 'age' | 'boost' | 'alphabetical';

const SORT_OPTIONS = {
  marketCap: 'Market Cap',
  volume: 'Volume 24h',
  age: 'Age',
  boost: 'Boost Amount',
  alphabetical: 'Alphabetical'
};

// Utility functions for formatting market data
function formatMarketCap(marketCap: number): string {
  if (marketCap >= 1e9) return `$${(marketCap / 1e9).toFixed(2)}B`;
  if (marketCap >= 1e6) return `$${(marketCap / 1e6).toFixed(2)}M`;
  if (marketCap >= 1e3) return `$${(marketCap / 1e3).toFixed(2)}K`;
  return `$${marketCap.toFixed(2)}`;
}

function formatVolume(volume: number): string {
  if (volume >= 1e9) return `$${(volume / 1e9).toFixed(2)}B`;
  if (volume >= 1e6) return `$${(volume / 1e6).toFixed(2)}M`;
  if (volume >= 1e3) return `$${(volume / 1e3).toFixed(2)}K`;
  return `$${volume.toFixed(2)}`;
}

function formatAge(timestamp: number): string {
  if (!timestamp) return 'Unknown';
  const now = Date.now();
  const diff = now - timestamp;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor(diff / (1000 * 60));

  if (days > 0) return `${days}d`;
  if (hours > 0) return `${hours}h`;
  if (minutes > 0) return `${minutes}m`;
  return 'Just now';
}

// Fetch additional market data for a token
async function fetchTokenMarketData(tokenAddress: string, chainId: string): Promise<Partial<TokenProfile>> {
  try {
    const response = await fetch(`https://api.dexscreener.com/latest/dex/tokens/${tokenAddress}`);
    if (!response.ok) return {};

    const data = await response.json();
    if (!data.pairs || data.pairs.length === 0) return {};

    // Get the most liquid pair (highest volume)
    const bestPair = data.pairs.reduce((best: any, current: any) =>
      (current.volume?.h24 || 0) > (best.volume?.h24 || 0) ? current : best
    );

    return {
      marketCap: bestPair.marketCap || 0,
      volume24h: bestPair.volume?.h24 || 0,
      priceUsd: parseFloat(bestPair.priceUsd || '0'),
      age: bestPair.pairCreatedAt ? new Date(bestPair.pairCreatedAt).getTime() : undefined,
    };
  } catch (error) {
    console.warn('Failed to fetch market data for', tokenAddress, error);
    return {};
  }
}

// Fetch boosted tokens from DexScreener
async function fetchBoostedTokens(): Promise<TokenProfile[]> {
  try {
    const response = await fetch('https://api.dexscreener.com/token-boosts/top/v1');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    const boostedTokens = Array.isArray(data) ? data : [data];

    // Fetch market data for each boosted token
    const tokensWithMarketData = await Promise.all(
      boostedTokens.map(async (token) => {
        const marketData = await fetchTokenMarketData(token.tokenAddress, token.chainId);
        return {
          ...token,
          ...marketData,
          symbol: token.tokenAddress.slice(0, 8), // Use truncated address as symbol if not provided
        };
      })
    );

    return tokensWithMarketData;
  } catch (error) {
    console.error('Error fetching boosted tokens:', error);
    throw error;
  }
}

interface TokenLink {
  type: string;
  label: string;
  url: string;
}

interface TokenProfile {
  url: string;
  chainId: string;
  symbol: string;
  tokenAddress: string;
  icon: string;
  header: string;
  description: string;
  links: TokenLink[];
  // Market data (fetched separately)
  marketCap?: number;
  volume24h?: number;
  priceUsd?: number;
  age?: number;
  holders?: number;
  // Boost data (for boosted tokens)
  totalAmount?: number;
  amount?: number;
  openGraph?: string;
}

// DexScreener chain validation
function isValidDexScreenerChain(chainId: string): boolean {
  const validChains = [
    'ethereum', 'solana', 'bsc', 'base', 'arbitrum',
    'polygon', 'avalanche', 'optimism', 'xrpl'
  ];

  return validChains.includes(chainId.toLowerCase());
}

// Build DexScreener embed URL with proper parameters
function buildDexScreenerEmbedUrl(tokenAddress: string, chainId: string): string {
  // Map chain IDs to DexScreener chain names
  const chainMap: { [key: string]: string } = {
    'ethereum': 'ethereum',
    'solana': 'solana',
    'bsc': 'bsc',
    'base': 'base',
    'arbitrum': 'arbitrum',
    'polygon': 'polygon',
    'avalanche': 'avalanche',
  };

  const chain = chainMap[chainId.toLowerCase()] || 'solana';

  // Build the embed URL with all the proper parameters
  const embedParams = new URLSearchParams({
    'embed': '1',
    'loadChartSettings': '0',
    'tabs': '0',
    'info': '0',
    'chartLeftToolbar': '0',
    'chartDefaultOnMobile': '1',
    'chartTheme': 'dark',
    'theme': 'dark',
    'chartStyle': '1',
    'chartType': 'usd',
    'interval': '15',
    'hideBranding': '1',
    'hideFooter': '1',
    'minimal': '1',
    'trades': '0'
  });

  return `https://dexscreener.com/${chain}/${tokenAddress}?${embedParams.toString()}`;
}

export function DexScreenerWidget({ isActive }: { isActive: boolean }) {
  const { messages } = useMessages();
  const [profiles, setProfiles] = useState<TokenProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<DexScreenerCategory>('new');
  const [categoryCache, setCategoryCache] = useState<Record<DexScreenerCategory, TokenProfile[]>>({
    new: [],
    boosted: []
  });
  const [sortBy, setSortBy] = useState<SortOption>('marketCap');

  // Generate unique widget ID for context tracking
  const [widgetId] = useState(() => `dexscreener_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`);
  const [threadId] = useState(() => {
    // Try to get thread ID from URL or context - for now use a default
    return window.location.hash.includes('thread_')
      ? window.location.hash.split('thread_')[1]?.split('/')[0] || 'default'
      : 'default';
  });
  const [selectedProfile, setSelectedProfile] = useState<TokenProfile | null>(null);
  const [isChartLoading, setIsChartLoading] = useState(true);

  useEffect(() => {
    const fetchAllCategories = async () => {
      if (!isActive) return;

      setError(null);
      setIsLoading(true);

      try {
        console.log('🔄 Fetching all DexScreener categories...');

        // Fetch both categories in parallel
        const [newTokensResponse, boostedTokensData] = await Promise.all([
          fetch('https://api.dexscreener.com/token-profiles/latest/v1'),
          fetchBoostedTokens().catch(err => {
            console.warn('Failed to fetch boosted tokens:', err);
            return [];
          })
        ]);

        // Process new tokens
        let newTokens: TokenProfile[] = [];
        if (newTokensResponse.ok) {
          const newTokensData = await newTokensResponse.json();
          const baseProfiles = Array.isArray(newTokensData) ? newTokensData : [newTokensData];

          // Fetch market data for each new token
          newTokens = await Promise.all(
            baseProfiles.map(async (profile) => {
              const marketData = await fetchTokenMarketData(profile.tokenAddress, profile.chainId);
              return { ...profile, ...marketData };
            })
          );
        }

        // Update cache with both categories
        const newCache = {
          new: newTokens,
          boosted: boostedTokensData
        };

        setCategoryCache(prev => ({ ...prev, ...newCache }));

        // Only update profiles if we're still on the same category and have data
        const currentCategoryTokens = newCache[viewMode] || [];
        if (currentCategoryTokens.length > 0) {
          setProfiles(currentCategoryTokens);
          console.log(`📋 Set tokens for current category '${viewMode}': ${currentCategoryTokens.length} tokens`);
        }

        console.log('🎉 All DexScreener categories fetched!', {
          'new': newCache.new.length,
          'boosted': newCache.boosted.length
        });

      } catch (error) {
        console.error('Error fetching profiles:', error);
        setError('Failed to load token profiles. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllCategories();
  }, [isActive, viewMode]);

  // Register widget context when data loads
  useEffect(() => {
    if (isActive && profiles.length > 0) {
      const contextData = formatDexScreenerContext('marketcap');
      registerWidgetContext(widgetId, 'dexscreener', threadId, contextData)
        .then(() => {
          console.log('📡 DexScreener widget context registered:', {
            widgetId,
            threadId,
            profileCount: profiles.length
          });
        })
        .catch(err => console.warn('⚠️ Failed to register DexScreener widget context:', err));
    }
  }, [isActive, profiles, widgetId, threadId]);

  // Sort profiles based on selected option
  const sortProfiles = (profiles: TokenProfile[], sortOption: SortOption): TokenProfile[] => {
    return [...profiles].sort((a, b) => {
      switch (sortOption) {
        case 'marketCap':
          return (b.marketCap || 0) - (a.marketCap || 0);
        case 'volume':
          return (b.volume24h || 0) - (a.volume24h || 0);
        case 'age':
          // Newer tokens first (higher timestamp = more recent)
          return (b.age || 0) - (a.age || 0);
        case 'boost':
          return (b.totalAmount || 0) - (a.totalAmount || 0);
        case 'alphabetical':
          return (a.tokenAddress || '').localeCompare(b.tokenAddress || '');
        default:
          return 0;
      }
    });
  };

  const filteredProfiles = profiles.filter(profile =>
    profile.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    profile.tokenAddress?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedProfiles = sortProfiles(filteredProfiles, sortBy);

  // Fetch a single category (fallback for cache misses)
  const fetchSingleCategory = async (category: DexScreenerCategory) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`🔗 Fetching ${category} tokens...`);
      let tokens: TokenProfile[] = [];

      if (category === 'new') {
        const response = await fetch('https://api.dexscreener.com/token-profiles/latest/v1');
        if (response.ok) {
          const data = await response.json();
          const baseProfiles = Array.isArray(data) ? data : [data];

          tokens = await Promise.all(
            baseProfiles.map(async (profile) => {
              const marketData = await fetchTokenMarketData(profile.tokenAddress, profile.chainId);
              return { ...profile, ...marketData };
            })
          );
        }
      } else if (category === 'boosted') {
        tokens = await fetchBoostedTokens();
      }

      // Update cache and profiles
      setCategoryCache(prev => ({ ...prev, [category]: tokens }));
      setProfiles(tokens);

      console.log(`✅ Successfully fetched ${tokens.length} ${category} tokens`);
    } catch (error) {
      console.error(`Error fetching ${category} tokens:`, error);
      setError(`Failed to load ${category} tokens. Please try again later.`);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to switch categories (smooth transition like PumpFun)
  const switchToCategory = (category: DexScreenerCategory) => {
    console.log(`🔄 Switching to category: ${category}`);
    console.log(`🔍 Current cache state:`, Object.keys(categoryCache).map(key => `${key}: ${categoryCache[key as DexScreenerCategory]?.length || 0} tokens`));

    // Auto-switch to appropriate sort for category
    if (category === 'boosted' && sortBy !== 'boost') {
      setSortBy('boost');
    } else if (category === 'new' && sortBy === 'boost') {
      setSortBy('marketCap');
    }

    // Check if we have this category cached
    const cachedTokens = categoryCache[category];

    if (cachedTokens && cachedTokens.length > 0) {
      console.log(`⚡ Using cached ${category} tokens (${cachedTokens.length} tokens)`);
      setProfiles(cachedTokens);
      setError(null);
      setViewMode(category);
    } else {
      console.log(`📥 ${category} not cached, fetching...`);
      setViewMode(category);
      fetchSingleCategory(category);
    }
  };

  // Handle token selection from remnants
  useEffect(() => {
    if (!isActive) return;
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage?.type === 'user' && lastMessage.content.includes('tokenAddress:')) {
      const tokenAddress = lastMessage.content.split('tokenAddress:')[1].trim();
      const profile = profiles.find(p => p.tokenAddress.toLowerCase() === tokenAddress.toLowerCase());
      if (profile) {
        setSelectedProfile(profile);
      }
    }
  }, [messages, profiles, isActive]);
  // Scroll to top when entering detail view
  useEffect(() => {
    if (selectedProfile && isActive) {
      setTimeout(() => window.scrollTo({ top: 0, behavior: 'smooth' }), 50);
    }
  }, [selectedProfile, isActive]);

  if (selectedProfile) {
    return (
      <div className="bg-[#111] rounded-2xl p-4 sm:p-6 lg:p-8 w-full mx-auto overflow-hidden">
        <div className="flex items-center gap-3 mb-6">
          <button
            onClick={() => setSelectedProfile(null)}
            className="hover:bg-[#222] p-2 rounded-lg transition-all duration-200 group hover:scale-105 hover:shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 group-hover:translate-x-[-2px] transition-transform" />
          </button>
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              Token Details
              <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full font-medium">
                {selectedProfile.chainId}
              </span>
            </h2>
          </div>
        </div>

        <div className="space-y-6">
          {/* Header Image */}
          {selectedProfile.header && (
            <div className="relative w-full h-48 rounded-xl overflow-hidden">
              <img
                src={selectedProfile.header}
                alt="Token Header"
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* Token Info */}
          <div className="bg-[#0A0A0A] rounded-xl p-4 sm:p-6 hover:ring-1 hover:ring-[#222] transition-all duration-200 group">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Column - Token Info */}
              <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-6">
                <img
                  src={selectedProfile.icon}
                  alt="Token Icon"
                  className="w-20 h-20 sm:w-24 sm:h-24 rounded-lg object-cover ring-1 ring-[#181818] group-hover:ring-[#22c55e] transition-all duration-200 shadow-lg group-hover:shadow-[#22c55e]/10 group-hover:scale-105"
                />
                <div className="flex-1 space-y-4">
                  <div className="space-y-2 text-center sm:text-left">
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(selectedProfile.tokenAddress);
                        // You could add a toast notification here
                      }}
                      className="text-xl sm:text-2xl font-semibold bg-gradient-to-r from-white to-gray-400 text-transparent bg-clip-text hover:from-[#22c55e] hover:to-emerald-400 transition-all duration-200 cursor-pointer group"
                      title="Click to copy contract address"
                    >
                      {selectedProfile.tokenAddress.slice(0, 8)}...{selectedProfile.tokenAddress.slice(-6)}
                      <span className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity text-sm">📋</span>
                    </button>
                    <p className="text-sm text-[#666] uppercase font-medium">{selectedProfile.chainId}</p>
                  </div>

                  <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2">
                    {selectedProfile.links.map((link, index) => (
                      <a
                        key={index}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={clsx(
                          "flex items-center gap-2 transition-all duration-200 px-3 py-1.5 rounded-lg text-sm hover:scale-105 hover:shadow-lg",
                          "text-[#666] bg-[#111]",
                          {
                            'hover:text-[#1DA1F2] hover:bg-[#1DA1F2]/5': link.type === 'twitter',
                            'hover:text-[#22c55e] hover:bg-[#22c55e]/5': link.type === 'website',
                            'hover:text-[#229ED9] hover:bg-[#229ED9]/5': link.type === 'telegram'
                          }
                        )}
                      >
                        {link.type === 'twitter' && <Twitter size={16} />}
                        {link.type === 'website' && <Globe size={16} />}
                        {link.type === 'telegram' && <MessageCircle size={16} />}
                        <span>{link.label}</span>
                      </a>
                    ))}

                    {/* DexScreener Link */}
                    <a
                      href={selectedProfile.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 transition-all duration-200 px-3 py-1.5 rounded-lg text-sm hover:scale-105 hover:shadow-lg text-[#666] bg-[#111] hover:text-[#22c55e] hover:bg-[#22c55e]/5"
                    >
                      <ExternalLink size={16} />
                      <span>DexScreener</span>
                    </a>
                  </div>
                </div>
              </div>

              {/* Right Column - Description */}
              {selectedProfile.description && (
                <div className="space-y-3 min-w-0">
                  <h4 className="font-medium text-sm flex items-center gap-2">
                    <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                    Description
                  </h4>
                  <div className="max-h-32 overflow-y-auto">
                    <p className="text-sm text-[#888] leading-relaxed break-words overflow-wrap-anywhere">
                      {selectedProfile.description}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Chart Section */}
          <div className="space-y-4">
            {/* Chart Header */}
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-white flex items-center gap-2">
                <span className="w-1 h-1 bg-[#22c55e] rounded-full"></span>
                Token Chart
              </h3>
            </div>

            {/* DexScreener Chart */}
            <div className="bg-[#0A0A0A] rounded-xl overflow-hidden border border-[#222] hover:border-[#333] transition-all duration-200">
              <div className="flex items-center justify-between p-4 border-b border-[#222]">
                <div className="flex items-center gap-2">
                  <ExternalLink className="w-4 h-4 text-[#22c55e]" />
                  <div>
                    <h3 className="font-medium text-white text-sm">DexScreener Chart</h3>
                    <p className="text-xs text-[#666]">Live trading data</p>
                  </div>
                </div>
                <a
                  href={selectedProfile.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-1.5 text-[#666] hover:text-white hover:bg-[#222] rounded-lg transition-all duration-200"
                  title="Open Full Chart"
                >
                  <ExternalLink className="w-3.5 h-3.5" />
                </a>
              </div>

              {/* DexScreener Embed with proper styling */}
              <div className="relative bg-[#111]">
                {isChartLoading && (
                  <div className="absolute inset-0 bg-[#111] bg-opacity-90 flex items-center justify-center z-10">
                    <div className="flex items-center gap-2 text-[#666]">
                      <div className="w-4 h-4 border border-[#22c55e] border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-sm">Loading chart...</span>
                    </div>
                  </div>
                )}
                <style jsx>{`
                  .dexscreener-embed {
                    position: relative;
                    width: 100%;
                    padding-bottom: 125%;
                    overflow: hidden;
                  }
                  @media(min-width: 1400px) {
                    .dexscreener-embed {
                      padding-bottom: 65%;
                    }
                  }
                  .dexscreener-embed iframe {
                    position: absolute;
                    width: 100%;
                    height: calc(100% + 40px);
                    top: 0;
                    left: 0;
                    border: 0;
                    /* Extend iframe height to push "Tracked by" banner below visible area */
                  }
                  /* Overlay to hide any remaining branding */
                  .dexscreener-embed::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 40px;
                    background: #111;
                    pointer-events: none;
                    z-index: 1;
                  }
                `}</style>
                <div className="dexscreener-embed">
                  <iframe
                    src={buildDexScreenerEmbedUrl(selectedProfile.tokenAddress, selectedProfile.chainId)}
                    title={`DexScreener Chart for ${selectedProfile.symbol}`}
                    loading="lazy"
                    sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
                    onLoad={() => setIsChartLoading(false)}
                    onError={() => {
                      console.error('DexScreener embed failed to load');
                      setIsChartLoading(false);
                    }}
                  />
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#111] rounded-2xl p-4 sm:p-6 w-full max-w-[95%] sm:max-w-[90%] mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="relative">
            <img
              src="https://dexscreener.com/favicon.ico"
              alt="DexScreener"
              className="w-8 h-8 rounded-lg"
            />
          </div>
          <h2 className="text-lg font-semibold">DexScreener Terminal</h2>
          <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full font-medium">LIVE</span>
        </div>

        {/* Category Tabs - Right Side */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => switchToCategory('new')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all duration-200 whitespace-nowrap ${
              viewMode === 'new'
                ? 'bg-[#22c55e] text-black font-medium'
                : 'text-[#666] hover:text-white hover:bg-[#181818]'
            }`}
          >
            🆕 New
          </button>

          <button
            onClick={() => switchToCategory('boosted')}
            className={`px-3 py-1.5 text-sm rounded-md transition-all duration-200 whitespace-nowrap ${
              viewMode === 'boosted'
                ? 'bg-[#22c55e] text-black font-medium'
                : 'text-[#666] hover:text-white hover:bg-[#181818]'
            }`}
          >
            🚀 Boosted
          </button>
        </div>
      </div>

      {/* Sort Controls */}
      <div className="mb-4">
        <div className="flex items-center gap-6 px-2 animate-fade-in">
          <div className="flex items-center gap-2 text-[#22c55e] bg-[#22c55e]/5 px-3 py-1.5 rounded-lg">
            <TrendingUp size={16} />
            <span className="text-sm font-medium">Sort by {SORT_OPTIONS[sortBy]}</span>
          </div>
          <button
            onClick={() => setSortBy('marketCap')}
            className={`text-sm transition-all duration-200 hover:scale-105 ${
              sortBy === 'marketCap' ? 'text-white' : 'text-[#666] hover:text-white'
            }`}
          >
            Market Cap
          </button>
          <button
            onClick={() => setSortBy('volume')}
            className={`text-sm transition-all duration-200 hover:scale-105 ${
              sortBy === 'volume' ? 'text-white' : 'text-[#666] hover:text-white'
            }`}
          >
            Volume
          </button>
          <button
            onClick={() => setSortBy('age')}
            className={`text-sm transition-all duration-200 hover:scale-105 ${
              sortBy === 'age' ? 'text-white' : 'text-[#666] hover:text-white'
            }`}
          >
            Age
          </button>
          {viewMode === 'boosted' && (
            <button
              onClick={() => setSortBy('boost')}
              className={`text-sm transition-all duration-200 hover:scale-105 ${
                sortBy === 'boost' ? 'text-white' : 'text-[#666] hover:text-white'
              }`}
            >
              Boost
            </button>
          )}
        </div>
      </div>

      {/* Search */}
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-[#666] w-4 h-4" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-[#0A0A0A] rounded-xl py-3 pl-11 pr-4 text-sm focus:outline-none focus:ring-1 focus:ring-[#222] placeholder-[#666] transition-all duration-200"
            placeholder="Search by description or address..."
          />
        </div>
      </div>

      {/* Token Grid */}
      <div className="mt-6 min-h-[400px]">
        {isLoading && profiles.length === 0 ? (
          <div className="text-center text-[#666] py-8">
            <div className="animate-pulse">Loading token profiles...</div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-8 flex items-center justify-center gap-2">
            <Info size={16} />
            <span>{error}</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            {sortedProfiles.map((profile) => (
              <div
                key={profile.tokenAddress}
                className="bg-[#0A0A0A] rounded-xl overflow-hidden hover:ring-1 hover:ring-[#222] transition-all duration-200 cursor-pointer group hover:scale-[1.02] flex flex-col"
                onClick={() => setSelectedProfile(profile)}
              >
                {/* Row 1: Banner Image */}
                <div className="relative w-full h-32 bg-[#111] flex-shrink-0">
                  <img
                    src={profile.header || profile.icon}
                    alt={profile.symbol}
                    className="w-full h-full object-cover"
                  />

                  {/* Boost Badge (only for boosted tokens) */}
                  {profile.totalAmount && viewMode === 'boosted' && (
                    <div className="absolute top-2 right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg flex items-center gap-1">
                      🚀 {profile.totalAmount}
                    </div>
                  )}
                </div>

                {/* Row 2: Profile + Token Info */}
                <div className="p-4 relative flex-shrink-0">
                  {/* Profile Picture */}
                  <div className="absolute -top-8 left-4">
                    <img
                      src={profile.icon}
                      alt={profile.symbol}
                      className="w-16 h-16 rounded-full object-cover ring-4 ring-[#0A0A0A] bg-[#111]"
                      onError={(e) => {
                        e.currentTarget.src = 'https://dexscreener.com/favicon.ico';
                      }}
                    />
                  </div>

                  <div className="mt-8 space-y-2">
                    <div className="flex items-center justify-between gap-2">
                      <h3 className="font-medium truncate text-sm">{profile.tokenAddress.slice(0, 8)}...{profile.tokenAddress.slice(-6)}</h3>
                      <span className="text-xs bg-[#22c55e]/10 text-[#22c55e] px-2 py-0.5 rounded-full">
                        {profile.chainId}
                      </span>
                    </div>
                    {profile.description && (
                      <p className="text-xs text-[#666] line-clamp-2">{profile.description}</p>
                    )}
                  </div>
                </div>

                {/* Row 3: Market Stats Grid */}
                <div className="px-4 pb-4 mt-auto">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-[#111] p-3 rounded-lg">
                      <div className="text-xs text-[#666] mb-1">Market Cap</div>
                      <div className="font-medium truncate text-[#22c55e] text-sm">
                        {profile.marketCap ? formatMarketCap(profile.marketCap) : 'N/A'}
                      </div>
                    </div>
                    <div className="bg-[#111] p-3 rounded-lg">
                      <div className="text-xs text-[#666] mb-1">Volume</div>
                      <div className="font-medium truncate text-orange-400 text-sm">
                        {profile.volume24h ? formatVolume(profile.volume24h) : 'N/A'}
                      </div>
                    </div>
                    <div className="bg-[#111] p-3 rounded-lg">
                      <div className="text-xs text-[#666] mb-1">Age</div>
                      <div className="font-medium truncate text-blue-400 text-sm">
                        {profile.age ? formatAge(profile.age) : 'N/A'}
                      </div>
                    </div>
                    <div className="bg-[#111] p-3 rounded-lg">
                      <div className="text-xs text-[#666] mb-1">Price</div>
                      <div className="font-medium truncate text-purple-400 text-sm">
                        {profile.priceUsd ? `$${profile.priceUsd.toFixed(6)}` : 'N/A'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}